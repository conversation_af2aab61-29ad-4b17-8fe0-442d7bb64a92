package com.sdwu.kotlin.screens

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.sdwu.kotlin.KotlinApplication
import com.sdwu.kotlin.di.AppContainer
import com.sdwu.kotlin.viewmodel.ProfileViewModel
import com.sdwu.kotlin.presentation.adapter.ViewSystemAdapter
import com.sdwu.kotlin.utils.DebugUtils
import com.sdwu.kotlin.utils.ErrorLogger
import com.sdwu.kotlin.utils.NavigationErrorHandler
import com.sdwu.kotlin.utils.NavigationTest
import com.sdwu.kotlin.utils.ProfileScreenTest
import com.sdwu.kotlin.utils.ComposeNavigationHelper

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(navController: NavController) {
    Log.d("ProfileScreen", "=== ProfileScreen开始初始化 ===")

    // 添加崩溃保护
    var hasInitError by remember { mutableStateOf(false) }
    var initErrorMessage by remember { mutableStateOf("") }

    // 如果初始化出错，显示错误页面
    if (hasInitError) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = "错误",
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "页面加载失败",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.error
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = initErrorMessage,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = {
                    val success = NavigationErrorHandler.safePopBackStack(navController, "profile_error")
                    if (!success) {
                        Log.e("ProfileScreen", "返回失败")
                    }
                }
            ) {
                Text("返回")
            }
        }
        return
    }

    // 页面加载监控
    ComposeNavigationHelper.MonitorPageLoad(
        pageName = "ProfileScreen",
        onLoadStart = { Log.d("ProfileScreen", "个人资料页面开始加载") },
        onLoadComplete = { Log.d("ProfileScreen", "个人资料页面加载完成") },
        onLoadError = { e ->
            Log.e("ProfileScreen", "个人资料页面加载失败", e)
            hasInitError = true
            initErrorMessage = "页面加载监控失败: ${e.message}"
        }
    )

    // 导航状态监控
    ComposeNavigationHelper.MonitorNavigationState(navController, "ProfileScreen")

    val context = LocalContext.current

    // 安全地获取AppContainer
    Log.d("ProfileScreen", "尝试获取Context和AppContainer")

    var app: KotlinApplication? = null
    var appContainer: AppContainer? = null

    try {
        Log.d("ProfileScreen", "获取ApplicationContext")
        val applicationContext = context.applicationContext
        Log.d("ProfileScreen", "ApplicationContext类型: ${applicationContext.javaClass.name}")

        app = applicationContext as? KotlinApplication
        if (app == null) {
            val errorMsg = "无法将applicationContext转换为KotlinApplication，实际类型: ${applicationContext.javaClass.name}"
            Log.e("ProfileScreen", errorMsg)
            hasInitError = true
            initErrorMessage = errorMsg
            return
        }
        Log.d("ProfileScreen", "成功获取KotlinApplication")

        Log.d("ProfileScreen", "尝试获取AppContainer")
        appContainer = app.appContainer
        Log.d("ProfileScreen", "成功获取AppContainer")

    } catch (e: Exception) {
        val errorMsg = "获取AppContainer失败: ${e.message}"
        Log.e("ProfileScreen", errorMsg, e)
        ErrorLogger.logError("ProfileScreen", errorMsg, e)
        hasInitError = true
        initErrorMessage = errorMsg
        return
    }

    // 生成调试报告和运行测试（在后台异步执行）
    LaunchedEffect(Unit) {
        try {
            ErrorLogger.logInfo("ProfileScreen", "开始生成调试报告")
            DebugUtils.generateDebugReport(context, navController)

            ErrorLogger.logInfo("ProfileScreen", "开始运行ProfileScreen测试")
            ProfileScreenTest.runAllTests(context)

            ErrorLogger.logInfo("ProfileScreen", "开始运行导航测试")
            NavigationTest.runAllNavigationTests(context, navController)

            ErrorLogger.logInfo("ProfileScreen", "生成导航诊断报告")
            NavigationTest.generateNavigationDiagnosticReport(navController)
        } catch (e: Exception) {
            Log.e("ProfileScreen", "调试报告生成失败", e)
            ErrorLogger.logError("ProfileScreen", "调试报告生成失败", e)
        }
    }

    // 创建ViewModel实例
    Log.d("ProfileScreen", "尝试创建ProfileViewModel")

    // 检查AppContainer和UserRepository
    LaunchedEffect(Unit) {
        try {
            Log.d("ProfileScreen", "检查AppContainer和UserRepository")
            val userRepository = appContainer?.userRepository
            if (userRepository == null) {
                val errorMsg = "UserRepository为null"
                Log.e("ProfileScreen", errorMsg)
                hasInitError = true
                initErrorMessage = errorMsg
                return@LaunchedEffect
            }
            Log.d("ProfileScreen", "UserRepository获取成功")
        } catch (e: Exception) {
            val errorMsg = "UserRepository检查失败: ${e.message}"
            Log.e("ProfileScreen", errorMsg, e)
            ErrorLogger.logError("ProfileScreen", errorMsg, e)
            hasInitError = true
            initErrorMessage = errorMsg
        }
    }

    // 如果初始化出错，不继续创建ViewModel
    if (hasInitError) {
        return
    }

    // 安全地创建ViewModel
    val userUseCase = appContainer?.userUseCase
    if (userUseCase == null) {
        Log.e("ProfileScreen", "UserUseCase为null，无法创建ViewModel")
        hasInitError = true
        initErrorMessage = "UserUseCase为null"
        return
    }

    val viewModel: ProfileViewModel = viewModel {
        ProfileViewModel(userUseCase)
    }

    LaunchedEffect(Unit) {
        Log.d("ProfileScreen", "ProfileViewModel创建成功")
    }

    // 收集UI状态
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(Unit) {
        try {
            Log.d("ProfileScreen", "开始收集UI状态")
        } catch (e: Exception) {
            val errorMsg = "UI状态收集失败: ${e.message}"
            Log.e("ProfileScreen", errorMsg, e)
            ErrorLogger.logError("ProfileScreen", errorMsg, e)
        }
    }



    // 编辑状态
    var editName by remember { mutableStateOf("") }
    var editEmail by remember { mutableStateOf("") }

    // 当用户数据加载时，更新编辑字段
    LaunchedEffect(uiState.user) {
        try {
            uiState.user?.let { user ->
                Log.d("ProfileScreen", "更新编辑字段: name=${user.name}, email=${user.email}")
                editName = user.name
                editEmail = user.email
            }
        } catch (e: Exception) {
            Log.e("ProfileScreen", "更新编辑字段失败", e)
        }
    }

    // 使用ViewSystemAdapter来统一处理状态管理
    ViewSystemAdapter(viewModel = viewModel) { vm ->
        ProfileContent(
            viewModel = vm,
            uiState = uiState,
            navController = navController,
            editName = editName,
            editEmail = editEmail,
            onEditNameChange = { editName = it },
            onEditEmailChange = { editEmail = it }
        )
    }
}

@Composable
private fun ProfileContent(
    viewModel: ProfileViewModel,
    uiState: com.sdwu.kotlin.viewmodel.ProfileUiState,
    navController: NavController,
    editName: String,
    editEmail: String,
    onEditNameChange: (String) -> Unit,
    onEditEmailChange: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部导航栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = {
                val success = NavigationErrorHandler.safePopBackStack(navController, "profile")
                if (!success) {
                    Log.e("ProfileScreen", "返回失败")
                }
            }) {
                Icon(Icons.Default.ArrowBack, contentDescription = "返回")
            }
            Text(
                text = "个人资料",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier
                    .padding(start = 8.dp)
                    .weight(1f)
            )

            // 编辑按钮
            if (!uiState.isEditMode) {
                IconButton(onClick = { viewModel.enterEditMode() }) {
                    Icon(Icons.Default.Edit, contentDescription = "编辑")
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // 加载状态
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 用户信息
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    if (uiState.isEditMode) {
                        // 编辑模式
                        OutlinedTextField(
                            value = editName,
                            onValueChange = onEditNameChange,
                            label = { Text("用户名") },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp)
                        )

                        OutlinedTextField(
                            value = editEmail,
                            onValueChange = onEditEmailChange,
                            label = { Text("邮箱") },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp)
                        )

                        Text(
                            text = "注册时间: ${uiState.user?.registrationDate ?: "未知"}",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        // 编辑按钮
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            OutlinedButton(
                                onClick = { viewModel.exitEditMode() }
                            ) {
                                Text("取消")
                            }

                            Button(
                                onClick = {
                                    val error = viewModel.validateUserInput(editName, editEmail)
                                    if (error == null) {
                                        viewModel.updateUserInfo(editName, editEmail)
                                    }
                                }
                            ) {
                                Text("保存")
                            }
                        }
                    } else {
                        // 显示模式
                        Text(
                            text = "用户名: ${uiState.user?.name ?: "未知"}",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        Text(
                            text = "邮箱: ${uiState.user?.email ?: "未知"}",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        Text(
                            text = "注册时间: ${uiState.user?.registrationDate ?: "未知"}",
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = {
                    val success = NavigationErrorHandler.safeNavigateTo(
                        navController = navController,
                        route = "settings",
                        from = "profile"
                    )
                    if (!success) {
                        Log.e("ProfileScreen", "导航到设置失败")
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("前往设置")
            }
        }

        // 错误信息显示
        uiState.error?.let { error ->
            Spacer(modifier = Modifier.height(16.dp))
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = error,
                    modifier = Modifier.padding(16.dp),
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

/**
 * 错误显示组件
 */
@Composable
private fun ErrorScreen(
    message: String,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "错误",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "出现错误",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )

        Spacer(modifier = Modifier.height(24.dp))

        Button(onClick = onRetry) {
            Text("返回")
        }
    }
}
