package com.sdwu.kotlin.domain.usecase

import com.sdwu.kotlin.data.model.ECGModels
import com.sdwu.kotlin.data.repository.ECGRepository
import kotlinx.coroutines.flow.Flow

/**
 * ECG相关业务逻辑用例
 * Domain层：处理心电图数据的业务规则
 */
class ECGUseCase(private val ecgRepository: ECGRepository) {
    
    /**
     * 获取ECG数据流
     */
    fun getECGDataStream(): Flow<List<ECGModels.ECGData>> {
        return ecgRepository.getECGDataStream()
    }
    
    /**
     * 添加ECG数据点
     */
    suspend fun addECGDataPoint(dataPoint: ECGModels.ECGData): Boolean {
        return try {
            // 业务逻辑：验证数据点
            if (!isValidECGData(dataPoint)) {
                return false
            }
            
            ecgRepository.addECGData(dataPoint)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 分析ECG数据
     */
    suspend fun analyzeECGData(data: List<ECGModels.ECGData>): ECGModels.ECGAnalysis {
        // 业务逻辑：ECG数据分析
        val heartRate = calculateHeartRate(data)
        val rhythm = analyzeRhythm(data)
        val quality = assessDataQuality(data)
        
        return ECGModels.ECGAnalysis(
            heartRate = heartRate,
            rhythm = rhythm,
            dataQuality = quality,
            timestamp = System.currentTimeMillis()
        )
    }
    
    private fun isValidECGData(dataPoint: ECGModels.ECGData): Boolean {
        return dataPoint.voltage in -5.0..5.0 && dataPoint.timestamp > 0
    }
    
    private fun calculateHeartRate(data: List<ECGModels.ECGData>): Int {
        // 简化的心率计算
        return if (data.isNotEmpty()) {
            (60 + (0..40).random()) // 模拟心率 60-100
        } else 0
    }
    
    private fun analyzeRhythm(data: List<ECGModels.ECGData>): String {
        return if (data.size > 100) "正常" else "数据不足"
    }
    
    private fun assessDataQuality(data: List<ECGModels.ECGData>): String {
        return when {
            data.isEmpty() -> "无数据"
            data.size < 50 -> "数据不足"
            else -> "良好"
        }
    }
}