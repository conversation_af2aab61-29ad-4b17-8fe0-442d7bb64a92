package com.sdwu.kotlin.viewmodel;

/**
 * 个人资料ViewModel
 * 管理用户信息的业务逻辑和UI状态
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0007\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\f\u001a\u00020\rJ\u0006\u0010\u000e\u001a\u00020\rJ\u0006\u0010\u000f\u001a\u00020\rJ\u0006\u0010\u0010\u001a\u00020\rJ\u0006\u0010\u0011\u001a\u00020\rJ\u0016\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014J\u0018\u0010\u0016\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/sdwu/kotlin/viewmodel/ProfileViewModel;", "Lcom/sdwu/kotlin/presentation/base/BaseViewModel;", "userUseCase", "Lcom/sdwu/kotlin/domain/usecase/UserUseCase;", "(Lcom/sdwu/kotlin/domain/usecase/UserUseCase;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/sdwu/kotlin/viewmodel/ProfileUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearProfileError", "", "enterEditMode", "exitEditMode", "loadUserProfile", "refreshUserInfo", "updateUserInfo", "name", "", "email", "validateUserInput", "Companion", "app_debug"})
public final class ProfileViewModel extends com.sdwu.kotlin.presentation.base.BaseViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.sdwu.kotlin.domain.usecase.UserUseCase userUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ProfileViewModel";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.sdwu.kotlin.viewmodel.ProfileUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.sdwu.kotlin.viewmodel.ProfileUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.sdwu.kotlin.viewmodel.ProfileViewModel.Companion Companion = null;
    
    public ProfileViewModel(@org.jetbrains.annotations.NotNull()
    com.sdwu.kotlin.domain.usecase.UserUseCase userUseCase) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.sdwu.kotlin.viewmodel.ProfileUiState> getUiState() {
        return null;
    }
    
    /**
     * 加载用户资料
     */
    public final void loadUserProfile() {
    }
    
    /**
     * 更新用户信息
     */
    public final void updateUserInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String email) {
    }
    
    /**
     * 进入编辑模式
     */
    public final void enterEditMode() {
    }
    
    /**
     * 退出编辑模式
     */
    public final void exitEditMode() {
    }
    
    /**
     * 刷新用户信息
     */
    public final void refreshUserInfo() {
    }
    
    /**
     * 清除错误状态
     */
    public final void clearProfileError() {
    }
    
    /**
     * 验证用户输入
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String validateUserInput(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String email) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/sdwu/kotlin/viewmodel/ProfileViewModel$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}